import { useEffect, useState } from 'react';
import { styled, t, SupersetClient } from '@superset-ui/core';
import { Form, FormItem } from 'src/components/Form';
import SubMenu from 'src/features/home/<USER>';
import { Switch } from 'src/components/Switch';
import { useForm } from 'antd/lib/form/Form';

interface Feature {
  id: number;
  name: string;
  description: string;
  enabled: boolean;
}

type FormValues = Record<string, boolean>;

const Styles = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 4}px;
`;

const features: Feature[] = [
  {
    id: 1,
    name: 'Feature 1',
    description: 'This is feature 1',
    enabled: true,
  },
  {
    id: 2,
    name: 'Feature 2',
    description: 'This is feature 2',
    enabled: false,
  },
  {
    id: 3,
    name: 'Feature 3',
    description: 'This is feature 3',
    enabled: true,
  },
];

const FeatureList = () => {
  const [form] = useForm<FormValues>();

  const handleSubmit = async () => {
    await form.validateFields();
    console.log(form.getFieldsValue());
  };

  useEffect(() => {
    const fetchFeatures = async () => {
      try {
        const response = await SupersetClient.get({
          endpoint: '/api/v1/features/',
        });
        const features = response.json.result;
        form.setFieldsValue(features);
      } catch (error) {
        console.log(error);
      }
    };
    fetchFeatures();
  }, []);

  return (
    <>
      <SubMenu name={t('Features')} />
      <Styles>
        <Form initialValues={features} form={form} onFinish={handleSubmit}>
          {features.map(feature => (
            <FormItem
              label={feature.name}
              key={feature.name}
              name={feature.name}
            >
              <Switch />
            </FormItem>
          ))}
        </Form>
      </Styles>
    </>
  );
};

export default FeatureList;
